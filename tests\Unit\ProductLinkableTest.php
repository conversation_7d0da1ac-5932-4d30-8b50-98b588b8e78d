<?php

namespace LBCDev\Ecommerce\Tests\Unit;

use Orchestra\Testbench\TestCase;
use LBCDev\Ecommerce\Models\Product;
use LBCDev\Ecommerce\Models\ProductLinkable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Fakes\FakeFile;
use Tests\Fakes\FakeCourse;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ProductLinkableTest extends TestCase
{
    use RefreshDatabase;

    protected function getPackageProviders($app)
    {
        return [
            \LBCDev\Ecommerce\EcommerceServiceProvider::class,
        ];
    }

    protected function getEnvironmentSetUp($app)
    {
        // Configurar SQLite en memoria
        $app['config']->set('database.default', 'sqlite');
        $app['config']->set('database.connections.sqlite', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);
    }

    public function test_puede_crear_un_product_linkable()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
            'meta' => ['size' => '2MB', 'format' => 'PDF'],
        ]);

        $this->assertInstanceOf(ProductLinkable::class, $linkable);
        $this->assertEquals($producto->id, $linkable->product_id);
        $this->assertEquals(FakeFile::class, $linkable->productable_type);
        $this->assertEquals(1, $linkable->productable_id);
        $this->assertEquals('primary', $linkable->purpose);
        $this->assertEquals('after_payment', $linkable->access_policy);
        $this->assertEquals(['size' => '2MB', 'format' => 'PDF'], $linkable->meta);
    }

    public function test_se_guarda_en_la_tabla_correcta()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'included',
            'access_policy' => 'immediate',
        ]);

        $this->assertDatabaseHas('lbcdev_ecommerce_productables', [
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => 1,
            'purpose' => 'included',
            'access_policy' => 'immediate',
        ]);
    }

    public function test_atributos_fillable_funcionan_correctamente()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(2, 'another-file.pdf');

        $attributes = [
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'bonus',
            'access_policy' => 'after_payment',
            'meta' => ['category' => 'document', 'priority' => 'high'],
        ];

        $linkable = ProductLinkable::create($attributes);

        foreach ($attributes as $key => $value) {
            $this->assertEquals($value, $linkable->$key);
        }
    }

    public function test_meta_se_castea_como_array()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');
        $metaData = ['type' => 'document', 'size' => '5MB', 'pages' => 10];

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
            'meta' => $metaData,
        ]);

        $this->assertIsArray($linkable->meta);
        $this->assertEquals($metaData, $linkable->meta);

        // Verificar que se puede acceder a elementos individuales del array
        $this->assertEquals('document', $linkable->meta['type']);
        $this->assertEquals('5MB', $linkable->meta['size']);
        $this->assertEquals(10, $linkable->meta['pages']);
    }

    public function test_meta_puede_ser_null()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
            'meta' => null,
        ]);

        $this->assertNull($linkable->meta);
    }

    public function test_relacion_product_funciona_correctamente()
    {
        $producto = Product::create([
            'name' => 'Producto con linkable',
            'price' => 150,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        // Verificar que la relación devuelve el tipo correcto
        $this->assertInstanceOf(BelongsTo::class, $linkable->product());

        // Verificar que la relación devuelve el producto correcto
        $this->assertInstanceOf(Product::class, $linkable->product);
        $this->assertEquals($producto->id, $linkable->product->id);
        $this->assertEquals('Producto con linkable', $linkable->product->name);
    }

    public function test_relacion_productable_funciona_correctamente()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        // Verificar que la relación devuelve el tipo correcto
        $this->assertInstanceOf(MorphTo::class, $linkable->productable());

        // Nota: La relación polimórfica no se puede probar completamente con modelos fake
        // ya que no están en la base de datos, pero podemos verificar que la relación existe
        $this->assertEquals(FakeFile::class, $linkable->productable_type);
        $this->assertEquals(1, $linkable->productable_id);
    }

    public function test_puede_crear_linkables_con_diferentes_tipos()
    {
        $producto = Product::create([
            'name' => 'Producto con múltiples linkables',
            'price' => 200,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'document.pdf');
        $fakeCourse = new FakeCourse(1, 'Curso de prueba');

        $linkableFile = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        $linkableCourse = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeCourse::class,
            'productable_id' => $fakeCourse->getKey(),
            'purpose' => 'included',
            'access_policy' => 'immediate',
        ]);

        $this->assertEquals(FakeFile::class, $linkableFile->productable_type);
        $this->assertEquals(FakeCourse::class, $linkableCourse->productable_type);
        $this->assertEquals($producto->id, $linkableFile->product_id);
        $this->assertEquals($producto->id, $linkableCourse->product_id);
    }

    public function test_puede_filtrar_por_purpose()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile1 = new FakeFile(1, 'primary-file.pdf');
        $fakeFile2 = new FakeFile(2, 'bonus-file.pdf');

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile1->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile2->getKey(),
            'purpose' => 'bonus',
            'access_policy' => 'after_payment',
        ]);

        $primaryLinkables = ProductLinkable::where('purpose', 'primary')->get();
        $bonusLinkables = ProductLinkable::where('purpose', 'bonus')->get();

        $this->assertCount(1, $primaryLinkables);
        $this->assertCount(1, $bonusLinkables);
        $this->assertEquals('primary', $primaryLinkables->first()->purpose);
        $this->assertEquals('bonus', $bonusLinkables->first()->purpose);
    }

    public function test_puede_filtrar_por_access_policy()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile1 = new FakeFile(1, 'immediate-file.pdf');
        $fakeFile2 = new FakeFile(2, 'after-payment-file.pdf');

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile1->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'immediate',
        ]);

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile2->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        $immediateLinkables = ProductLinkable::where('access_policy', 'immediate')->get();
        $afterPaymentLinkables = ProductLinkable::where('access_policy', 'after_payment')->get();

        $this->assertCount(1, $immediateLinkables);
        $this->assertCount(1, $afterPaymentLinkables);
        $this->assertEquals('immediate', $immediateLinkables->first()->access_policy);
        $this->assertEquals('after_payment', $afterPaymentLinkables->first()->access_policy);
    }

    public function test_puede_actualizar_atributos_del_linkable()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
            'meta' => ['version' => '1.0'],
        ]);

        // Actualizar atributos
        $linkable->update([
            'purpose' => 'bonus',
            'access_policy' => 'immediate',
            'meta' => ['version' => '2.0', 'updated' => true],
        ]);

        $this->assertEquals('bonus', $linkable->purpose);
        $this->assertEquals('immediate', $linkable->access_policy);
        $this->assertEquals(['version' => '2.0', 'updated' => true], $linkable->meta);

        // Verificar en base de datos
        $this->assertDatabaseHas('lbcdev_ecommerce_productables', [
            'id' => $linkable->id,
            'purpose' => 'bonus',
            'access_policy' => 'immediate',
        ]);
    }

    public function test_puede_eliminar_linkable()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        $linkableId = $linkable->id;

        // Verificar que existe
        $this->assertDatabaseHas('lbcdev_ecommerce_productables', [
            'id' => $linkableId,
        ]);

        // Eliminar
        $linkable->delete();

        // Verificar que se eliminó
        $this->assertDatabaseMissing('lbcdev_ecommerce_productables', [
            'id' => $linkableId,
        ]);
    }

    public function test_relacion_con_producto_se_mantiene_consistente()
    {
        $producto1 = Product::create([
            'name' => 'Producto 1',
            'price' => 100,
            'is_active' => true,
        ]);

        $producto2 = Product::create([
            'name' => 'Producto 2',
            'price' => 200,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto1->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        // Verificar relación inicial
        $this->assertEquals($producto1->id, $linkable->product->id);
        $this->assertEquals('Producto 1', $linkable->product->name);

        // Cambiar producto
        $linkable->update(['product_id' => $producto2->id]);
        $linkable->refresh();

        // Verificar nueva relación
        $this->assertEquals($producto2->id, $linkable->product->id);
        $this->assertEquals('Producto 2', $linkable->product->name);
    }

    public function test_puede_crear_multiples_linkables_para_mismo_producto()
    {
        $producto = Product::create([
            'name' => 'Producto con múltiples linkables',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile1 = new FakeFile(1, 'file1.pdf');
        $fakeFile2 = new FakeFile(2, 'file2.pdf');
        $fakeCourse = new FakeCourse(1, 'course1');

        $linkable1 = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile1->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        $linkable2 = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile2->getKey(),
            'purpose' => 'bonus',
            'access_policy' => 'immediate',
        ]);

        $linkable3 = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeCourse::class,
            'productable_id' => $fakeCourse->getKey(),
            'purpose' => 'included',
            'access_policy' => 'after_payment',
        ]);

        // Verificar que todos los linkables pertenecen al mismo producto
        $linkablesDelProducto = ProductLinkable::where('product_id', $producto->id)->get();
        $this->assertCount(3, $linkablesDelProducto);

        // Verificar que cada linkable tiene sus propios atributos
        $this->assertEquals('primary', $linkable1->purpose);
        $this->assertEquals('bonus', $linkable2->purpose);
        $this->assertEquals('included', $linkable3->purpose);
    }

    public function test_meta_array_vacio_se_maneja_correctamente()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
            'meta' => [],
        ]);

        $this->assertIsArray($linkable->meta);
        $this->assertEmpty($linkable->meta);
        $this->assertEquals([], $linkable->meta);
    }

    public function test_timestamps_se_crean_automaticamente()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        $this->assertNotNull($linkable->created_at);
        $this->assertNotNull($linkable->updated_at);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $linkable->created_at);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $linkable->updated_at);
    }

    public function test_puede_buscar_por_productable_type()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'file.pdf');
        $fakeCourse = new FakeCourse(1, 'course');

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeCourse::class,
            'productable_id' => $fakeCourse->getKey(),
            'purpose' => 'included',
            'access_policy' => 'immediate',
        ]);

        $fileLinkables = ProductLinkable::where('productable_type', FakeFile::class)->get();
        $courseLinkables = ProductLinkable::where('productable_type', FakeCourse::class)->get();

        $this->assertCount(1, $fileLinkables);
        $this->assertCount(1, $courseLinkables);
        $this->assertEquals(FakeFile::class, $fileLinkables->first()->productable_type);
        $this->assertEquals(FakeCourse::class, $courseLinkables->first()->productable_type);
    }

    public function test_puede_buscar_por_productable_id()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile1 = new FakeFile(1, 'file1.pdf');
        $fakeFile2 = new FakeFile(2, 'file2.pdf');

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile1->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile2->getKey(),
            'purpose' => 'bonus',
            'access_policy' => 'immediate',
        ]);

        $linkable1 = ProductLinkable::where('productable_id', 1)->first();
        $linkable2 = ProductLinkable::where('productable_id', 2)->first();

        $this->assertNotNull($linkable1);
        $this->assertNotNull($linkable2);
        $this->assertEquals(1, $linkable1->productable_id);
        $this->assertEquals(2, $linkable2->productable_id);
        $this->assertEquals('primary', $linkable1->purpose);
        $this->assertEquals('bonus', $linkable2->purpose);
    }

    public function test_puede_combinar_filtros_multiples()
    {
        $producto = Product::create([
            'name' => 'Producto de prueba',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile1 = new FakeFile(1, 'file1.pdf');
        $fakeFile2 = new FakeFile(2, 'file2.pdf');

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile1->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile2->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'immediate',
        ]);

        // Buscar linkables con purpose 'primary' y access_policy 'immediate'
        $linkables = ProductLinkable::where('purpose', 'primary')
            ->where('access_policy', 'immediate')
            ->get();

        $this->assertCount(1, $linkables);
        $this->assertEquals('primary', $linkables->first()->purpose);
        $this->assertEquals('immediate', $linkables->first()->access_policy);
        $this->assertEquals(2, $linkables->first()->productable_id);
    }

    public function test_relacion_product_con_eager_loading()
    {
        $producto = Product::create([
            'name' => 'Producto con eager loading',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'test-file.pdf');

        ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
        ]);

        // Cargar linkables con relación product
        $linkables = ProductLinkable::with('product')->get();

        $this->assertCount(1, $linkables);
        $this->assertTrue($linkables->first()->relationLoaded('product'));
        $this->assertEquals('Producto con eager loading', $linkables->first()->product->name);
    }

    public function test_modelo_usa_tabla_correcta()
    {
        $linkable = new ProductLinkable();
        $this->assertEquals('lbcdev_ecommerce_productables', $linkable->getTable());
    }

    public function test_fillable_contiene_todos_los_campos_esperados()
    {
        $linkable = new ProductLinkable();
        $expectedFillable = [
            'product_id',
            'productable_type',
            'productable_id',
            'purpose',
            'access_policy',
            'meta',
        ];

        $this->assertEquals($expectedFillable, $linkable->getFillable());
    }

    public function test_casts_contiene_configuracion_correcta()
    {
        $linkable = new ProductLinkable();
        $expectedCasts = [
            'meta' => 'array',
        ];

        $this->assertEquals($expectedCasts, $linkable->getCasts());
    }

    public function test_relaciones_devuelven_tipos_correctos()
    {
        $linkable = new ProductLinkable();

        // Verificar que los métodos de relación existen
        $this->assertTrue(method_exists($linkable, 'product'));
        $this->assertTrue(method_exists($linkable, 'productable'));

        // Verificar que devuelven los tipos de relación correctos
        $this->assertInstanceOf(BelongsTo::class, $linkable->product());
        $this->assertInstanceOf(MorphTo::class, $linkable->productable());
    }

    public function test_puede_crear_linkable_con_valores_minimos()
    {
        $producto = Product::create([
            'name' => 'Producto mínimo',
            'price' => 50,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'minimal-file.pdf');

        // Crear linkable solo con campos obligatorios
        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
        ]);

        $this->assertInstanceOf(ProductLinkable::class, $linkable);
        $this->assertEquals($producto->id, $linkable->product_id);
        $this->assertEquals(FakeFile::class, $linkable->productable_type);
        $this->assertEquals(1, $linkable->productable_id);
        $this->assertNull($linkable->purpose);
        $this->assertNull($linkable->access_policy);
        $this->assertNull($linkable->meta);
    }

    public function test_meta_con_datos_complejos()
    {
        $producto = Product::create([
            'name' => 'Producto con meta complejo',
            'price' => 100,
            'is_active' => true,
        ]);

        $fakeFile = new FakeFile(1, 'complex-file.pdf');

        $complexMeta = [
            'file_info' => [
                'size' => '5MB',
                'format' => 'PDF',
                'pages' => 25,
                'encrypted' => true,
            ],
            'access_rules' => [
                'download_limit' => 3,
                'expiry_days' => 30,
                'ip_restrictions' => ['***********/24'],
            ],
            'tags' => ['important', 'confidential', 'premium'],
            'created_by' => 'admin',
            'version' => '2.1.0',
        ];

        $linkable = ProductLinkable::create([
            'product_id' => $producto->id,
            'productable_type' => FakeFile::class,
            'productable_id' => $fakeFile->getKey(),
            'purpose' => 'primary',
            'access_policy' => 'after_payment',
            'meta' => $complexMeta,
        ]);

        $this->assertEquals($complexMeta, $linkable->meta);
        $this->assertEquals('5MB', $linkable->meta['file_info']['size']);
        $this->assertEquals(3, $linkable->meta['access_rules']['download_limit']);
        $this->assertContains('premium', $linkable->meta['tags']);
    }
}
